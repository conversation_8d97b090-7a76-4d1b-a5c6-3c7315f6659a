import React, { useState } from "react";
import CustomModal from "../../CustomModal";
import clsx from "clsx";

interface BillDemonstrationProps {
  isOpen: boolean;
  onClose: () => void;
}

interface DenominationRow {
  amount: number;
  quantity: string;
  total: string;
}

const BillDemonstration: React.FC<BillDemonstrationProps> = ({ isOpen, onClose }) => {
  const [denominations, setDenominations] = useState<DenominationRow[]>([
    { amount: 0.01, quantity: "", total: "" },
    { amount: 0.05, quantity: "", total: "" },
    { amount: 0.10, quantity: "", total: "" },
    { amount: 0.25, quantity: "", total: "" },
    { amount: 1.00, quantity: "", total: "" },
    { amount: 2.00, quantity: "", total: "" },
    { amount: 5.00, quantity: "", total: "" },
    { amount: 5.00, quantity: "", total: "" },
    { amount: 5.00, quantity: "", total: "" },
    { amount: 5.00, quantity: "", total: "" },
    { amount: 5.00, quantity: "", total: "" },
    { amount: 10.00, quantity: "", total: "" },
  ]);

  // const [totalCash, setTotalCash] = useState(10502.52);
  const totalCash = 10502.52;

  const handleQuantityChange = (index: number, value: string) => {
    const newDenominations = [...denominations];
    newDenominations[index].quantity = value;
    newDenominations[index].total = (Number(value) * newDenominations[index].amount).toFixed(2);
    setDenominations(newDenominations);
  };

  const numberPadButtons = [
    ['1', '2', '3', '10'],
    ['4', '5', '6', '20'],
    ['7', '8', '9', 'X'],
    ['C', '0', '.', 'Add'],
  ];

  const footer = (
    <div className="flex gap-4 justify-end">
      <button
        onClick={onClose}
        className="px-10 py-2 border text-orange border-orange text-xl font-semibold rounded-full cursor-pointer"
      >
        Cancel
      </button>
    </div>
  );

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title="Bill Demonstration"
      width="max-w-6xl"
      footer={footer}
    >
      <div className="p-6 flex gap-8">
        <div className="flex-1">
          <div className="space-y-4">
            {/* Headers */}
            <div className="flex gap-4 mb-2 text-xs text-natural">
              <div className="w-36 ">
                Demonstration
              </div>
              <div className="flex-1">
                Quantity
              </div>
              <div className="flex-1">
                Total
              </div>
            </div>
            
            {/* Denomination rows */}
            {denominations.map((row, index) => (
              <div key={index} className="flex gap-4">
                <div className="w-36 bg-[#E4E4E4] p-2 px-5 text-center rounded-lg">
                  ${row.amount.toFixed(2)}
                </div>
                <input
                  type="number"
                  value={row.quantity}
                  onChange={(e) => handleQuantityChange(index, e.target.value)}
                  placeholder="Enter Amount"
                  className="flex-1 p-2 border border-[#E4E4E4] rounded-xl focus:outline-none"
                />
                <input
                  type="text"
                  value={row.total}
                  readOnly
                  placeholder="Total"
                  className="flex-1 p-2 border border-[#E4E4E4] rounded-xl focus:outline-none"
                />
              </div>
            ))}
          </div>
        </div>

        <div className="flex-1">
          <div className="grid grid-cols-4 gap-2">
            {numberPadButtons.map((row, rowIndex) => (
              row.map((button, colIndex) => (
                <button
                  key={`${rowIndex}-${colIndex}`}
                  className={clsx(
                    "h-16 rounded-3xl font-bold text-lg",
                    button === 'X' && "text-orange",
                    button === 'C' && " text-orange-600",
                    button === 'Add' && " text-black font-blod",
                    button === '10' && "bg-blue-100 text-blue-600",
                    button === '20' && "bg-blue-100 text-blue-600",
                    !['X', 'C', 'Add', '10', '20'].includes(button) && "bg-gray-100"
                  )}
                >
                  {button}
                </button>
              ))
            ))}
          </div>

          <div className="mt-8 bg-gray-50 p-4 rounded-lg">
            <div className="flex flex-col font-bold justify-between items-center">
              <span className="block text-2xl">Total Cash on Hand</span>
              <span className="block text-2xl font-bold text-orange">
                $ {totalCash.toFixed(2)}
              </span>
            </div>
          </div>
        </div>
      </div>
    </CustomModal>
  );
};

export default BillDemonstration;

