import React from "react";
import { useDispatch } from "react-redux";
import CustomModal from "../CustomModal";
import { FiPrinter } from "react-icons/fi";
import { FaMoneyBill } from "react-icons/fa";
import { loadOrderData } from "../../store/slices/cartSlice";

// Define the structure of the order item
interface OrderItem {
  ProductId?: string;
  Product_pic?: string;
  active?: string;
  barCode?: string;
  categoryId?: any[];
  categoryParents?: any[];
  courseDate?: any[];
  hasPicture?: boolean;
  ingredient?: any[];
  isLock?: boolean;
  loyalityOffer?: any;
  modifiers?: {
    productId: string;
    data: any[];
    price: number;
  };
  name: string;
  price: number;
  quantity: number;
  retailPrice?: number;
  reviewId?: any[];
  shortDescription?: string;
  totalQuantity?: number;
  _id?: string;
}

// Define the structure matching the PurchaseRecord interface
interface PurchaseRecord {
  receiptNumber: string;
  operatorName: string;
  customer: string;
  points: number;
  recordDate: string;
  tax: number | Array<{ name: string; addtax: number }>;
  amount: number;
  receivedAmount: number;
  dueAmount: number;
  date: string;
  product: OrderItem[];
  // Additional fields from the API response
  grandTotal?: number;
  paymentType?: string;
}

interface OrderDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  order?: PurchaseRecord;
}

const OrderDetailsModal: React.FC<OrderDetailsModalProps> = ({
  isOpen,
  onClose,
  order
}) => {
  const dispatch = useDispatch();

  // Handle the print functionality
  const handlePrint = () => {
    window.print();
  };

  // Handle the load functionality
  const handleLoad = () => {
    if (order && order.product) {
      console.log("Loading order...", order);

      // Calculate tax percentage from order tax data
      let taxPercentage = 0;
      const itemSubtotal = order.product.reduce((sum, item) => sum + (item.price * item.quantity), 0);

      if (typeof order.tax === 'number') {
        // If tax is a number (tax amount), calculate percentage based on subtotal
        taxPercentage = itemSubtotal > 0 ? (order.tax / itemSubtotal) * 100 : 0;
      } else if (Array.isArray(order.tax)) {
        // If tax is an array, sum up all tax percentage values (assuming they are percentages)
        taxPercentage = order.tax.reduce((sum: number, t: { name: string; addtax: number }) => sum + (t.addtax || 0), 0);
      }

      console.log("Tax calculation:", {
        orderTax: order.tax,
        itemSubtotal,
        calculatedTaxPercentage: taxPercentage
      });

      // Extract discount information if available
      // You can add discount extraction logic here if the order has discount fields
      const discountAmount = '0'; // Default to 0, can be updated if order has discount data

      // Load order data into cart
      dispatch(loadOrderData({
        items: order.product,
        discountAmount: discountAmount,
        taxPercentage: taxPercentage,
        invoiceNumber: order.receiptNumber || undefined
      }));
    }
    onClose();
  };

  console.log("the order from the order modal", order);

  // We don't need orderData in this version of the modal

  // Get product items from the order
  const products: OrderItem[] = order?.product || [];

  // Calculate subtotal
  const subtotal = products.reduce((sum, item) => sum + (item.price * item.quantity), 0);

  // Get tax from the order - handle both number and object formats
  const tax = typeof order?.tax === 'number' ? order.tax :
    Array.isArray(order?.tax) ? order.tax.reduce((sum: number, t: { name: string; addtax: number }) => sum + (t.addtax || 0), 0) : 0;

  // Get total amount from the order
  const totalAmount = order?.amount || order?.grandTotal || subtotal;

  // Get payment method
  const paymentMethod = order?.paymentType || "Cash";

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title="Order Details"
      width="max-w-2xl"
    >
      <div className="p-4">
        {/* Header */}
        <h2 className="text-xl font-semibold text-center mb-4">Order Details</h2>

        {/* Table Header */}
        <div className="grid grid-cols-4 gap-4 border-b pb-2 mb-2 text-gray-500 font-medium">
          <div>ITEM NAME</div>
          <div className="text-center">QTY</div>
          <div className="text-center">PRICE</div>
          <div className="text-right">SUBTOTAL</div>
        </div>

        {/* Order Items */}
        <div className="space-y-4 mb-4">
          {products.map((item: OrderItem, index: number) => (
            <div key={item.ProductId || index} className="grid grid-cols-4 gap-4 border-b pb-2">
              <div className="font-medium">{item.name}</div>
              <div className="text-center">{item.quantity}</div>
              <div className="text-center">${item.price.toFixed(2)}</div>
              <div className="text-right">${(item.price * item.quantity).toFixed(2)}</div>
            </div>
          ))}
        </div>

        {/* Notes Section */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="border rounded p-3">
            <div className="font-medium mb-2">Notes</div>
            <p className="text-sm text-gray-600">
              Lorem ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book.
            </p>
          </div>

          {/* Order Summary */}
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Sub Total</span>
              <span className="font-medium">${subtotal.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span>Surcharge</span>
              <span className="font-medium">$0.00</span>
            </div>
            <div className="flex justify-between">
              <span>Order Discount</span>
              <span className="font-medium">$0.00</span>
            </div>
            <div className="flex justify-between">
              <span>Tax</span>
              <span className="font-medium">${tax.toFixed(2)}</span>
            </div>
            <div className="flex justify-between text-orange-500">
              <span className="font-bold">Bill Amount</span>
              <span className="font-bold">${totalAmount.toFixed(2)}</span>
            </div>
          </div>
        </div>

        {/* Payment Method */}
        <div className="flex items-center mb-4">
          <span className="mr-2">Payment Method</span>
          <div className="flex items-center bg-green-100 text-green-800 px-2 py-1 rounded">
            <FaMoneyBill className="mr-1" />
            <span>{paymentMethod}</span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between">
          <button
            onClick={onClose}
            className="px-8 py-2 border border-gray-300 rounded-full text-gray-700 font-medium"
          >
            Cancel
          </button>

          <button
            onClick={handlePrint}
            className="px-8 py-2 bg-gray-800 text-white rounded-full font-medium flex items-center justify-center"
          >
            Reprint <FiPrinter className="ml-2" />
          </button>

          <button
            onClick={handleLoad}
            className="px-8 py-2 bg-orange-500 text-white rounded-full font-medium"
          >
            Load
          </button>
        </div>
      </div>
    </CustomModal>
  );
};

export default OrderDetailsModal;