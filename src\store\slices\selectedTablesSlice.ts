import { createSlice } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../store';

interface SelectedTablesState {
  selectedTables: string[];
}

const initialState: SelectedTablesState = {
  selectedTables: [],
};

const selectedTablesSlice = createSlice({
  name: 'selectedTables',
  initialState,
  reducers: {
    addTable: (state, action: PayloadAction<string>) => {
      // Only add if not already in the array
      if (!state.selectedTables.includes(action.payload)) {
        state.selectedTables.push(action.payload);
      }
    },
    removeTable: (state, action: PayloadAction<string>) => {
      state.selectedTables = state.selectedTables.filter(
        (tableId) => tableId !== action.payload
      );
    },
    clearSelectedTables: (state) => {
      state.selectedTables = [];
    },
    setSelectedTables: (state, action: PayloadAction<string[]>) => {
      state.selectedTables = action.payload;
    },
  },
});

export const {
  addTable,
  removeTable,
  clearSelectedTables,
  setSelectedTables
} = selectedTablesSlice.actions;

export const selectSelectedTables = (state: RootState) => state.selectedTables.selectedTables;

export default selectedTablesSlice.reducer;
