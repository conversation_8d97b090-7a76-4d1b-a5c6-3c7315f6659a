import React, { useState, useEffect } from 'react';
import CustomModal from '../../CustomModal';

interface TipAmountModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddTip: (amount: string) => void;
}

const TipAmountModal: React.FC<TipAmountModalProps> = ({ isOpen, onClose, onAddTip }) => {
  const [inputAmount, setInputAmount] = useState('');

  // Reset input amount when modal is closed
  useEffect(() => {
    if (!isOpen) {
      setInputAmount('');
    }
  }, [isOpen]);

  // Handle add tip
  const handleAddTip = () => {
    onAddTip(inputAmount);
    setInputAmount(''); // Reset input
    onClose();
  };

  // Handle preset amount selection
  const handlePresetAmount = (amount: string) => {
    setInputAmount(amount);
  };

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title="Tip amount"
      width="max-w-sm"
      zIndex={1000000} // Extremely high z-index to appear above other modals
      footer={null}
    >
      <div className="p-6 flex flex-col gap-4">
        {/* Input field */}
        <div className="mb-4">
          <input
            type="text"
            className="w-full p-3 border border-gray-200 rounded-lg text-gray-700 text-center text-lg placeholder-gray-400 focus:outline-none focus:border-gray-300"
            value={inputAmount ? inputAmount : ''}
            onChange={(e) => setInputAmount(e.target.value)}
            placeholder="Input amount"
          />
        </div>

        {/* Preset amounts */}
        <div className="grid grid-cols-3 gap-4 mb-4">
          <button
            onClick={() => handlePresetAmount('5')}
            className="py-2 px-4 border border-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors"
          >
            $5
          </button>
          <button
            onClick={() => handlePresetAmount('10')}
            className="py-2 px-4 border border-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors"
          >
            $10
          </button>
          <button
            onClick={() => handlePresetAmount('25')}
            className="py-2 px-4 border border-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors"
          >
            $25
          </button>
        </div>

        {/* Action buttons */}
        <div className="flex gap-4">
          <button
            onClick={onClose}
            className="flex-1 py-2 px-4 border border-gray-200 text-gray-700 font-medium rounded-full hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleAddTip}
            className="flex-1 py-2 px-4 bg-orange-500 text-white font-medium rounded-full hover:bg-orange-600 transition-colors"
            disabled={!inputAmount}
          >
            Add tip amount
          </button>
        </div>
      </div>
    </CustomModal>
  );
};

export default TipAmountModal;
